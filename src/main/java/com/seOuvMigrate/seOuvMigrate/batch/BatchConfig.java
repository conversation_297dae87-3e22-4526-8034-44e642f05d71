package com.seOuvMigrate.seOuvMigrate.batch;

import com.seOuvMigrate.seOuvMigrate.domain.*;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.*;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableBatchProcessing

public class BatchConfig {

    @Bean
    public Job migrateUsuarioJob(
            JobRepository jobRepository,
//            Step perfilStep, inutilizado
//            Step usuarioStep, Passo 1 - já migrado
//            Step responsavelStep, // Passo 2 - já migrado
//            Step entidadeStep // Passo 3 - já migrado
//            Step temaStep // Passo 4 - já migrado
//            Step cidadaoStep // Passo 5 - já migrado
//            Step competenciaStep // Passo 6 - já migrado
            Step solicitacaoStep, // Passo 7 - executar primeiro
            Step mensagemStep, // Passo 8 - executar após solicitacao
            Step anexoStep // Passo 9 - executar após mensagem


    ) {
        return new JobBuilder("migrateUsuarioJob", jobRepository)
                .incrementer(new RunIdIncrementer())
                .start(solicitacaoStep)
                .next(mensagemStep)
                .next(anexoStep)
                .build();
    }

    @Bean
    public Step perfilStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("perfilReader") ItemReader<PerfilOld> reader,
            PerfilProcessor processor,
            @Qualifier("perfilWriter") ItemWriter<PerfilNew> writer  // <- PerfilNew aqui
    ) {
        return new StepBuilder("perfilStep", jobRepository)
                .<PerfilOld, PerfilNew>chunk(100, txManager)         // <- PerfilNew aqui
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }




    @Bean
    public Step usuarioStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("usuarioReader") ItemReader<UsuarioOld> reader,
            UsuarioProcessor processor,
            @Qualifier("usuarioWriter") ItemWriter<UsuarioNew> writer
    ) {
        return new StepBuilder("usuarioStep", jobRepository)
                .<UsuarioOld,UsuarioNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    @Bean
    public Step responsavelStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("responsavelReader") ItemReader<ResponsavelOld> reader,
            ResponsavelProcessor processor,
            @Qualifier("responsavelWriter") ItemWriter<ResponsavelNew> writer
    ) {
        return new StepBuilder("responsavelStep", jobRepository)
                .<ResponsavelOld, ResponsavelNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }





    @Bean
    public Step entidadeStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("entidadeReader") ItemReader<EntidadeOld> reader,
            EntidadeProcessor processor,
            @Qualifier("entidadeWriter") ItemWriter<EntidadeNew> writer
    ) {
        return new StepBuilder("entidadeStep", jobRepository)
                .<EntidadeOld, EntidadeNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    @Bean
    public Step temaStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            JdbcCursorItemReader<TemaOld> reader,
            TemaProcessor processor,
            JdbcBatchItemWriter<TemaNew> writer
    ) {
        return new StepBuilder("temaStep", jobRepository)
                .<TemaOld, TemaNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    @Bean
    public Step cidadaoStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            ItemReader<CidadaoOld> reader,
            CidadaoProcessor processor,
            ItemWriter<CidadaoNew> writer
    ) {
        return new StepBuilder("cidadaoStep", jobRepository)
                .<CidadaoOld, CidadaoNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    @Bean
    public Step competenciaStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("competenciaReader") ItemReader<CompetenciaOld> reader,
            CompetenciaProcessor processor,
            @Qualifier("competenciaWriter") ItemWriter<CompetenciaNew> writer
    ) {
        return new StepBuilder("competenciaStep", jobRepository)
                .<CompetenciaOld, CompetenciaNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    @Bean
    public Step solicitacaoStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("solicitacaoReader") ItemReader<SolicitacaoOld> reader,
            SolicitacaoProcessor processor,
            @Qualifier("solicitacaoWriter") ItemWriter<SolicitacaoNew> writer
    ) {
        return new StepBuilder("solicitacaoStep", jobRepository)
                .<SolicitacaoOld, SolicitacaoNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    @Bean
    public Step mensagemStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("mensagemReader") ItemReader<MensagemOld> reader,
            MensagemProcessor processor,
            @Qualifier("mensagemWriter") ItemWriter<MensagemNew> writer
    ) {
        return new StepBuilder("mensagemStep", jobRepository)
                .<MensagemOld, MensagemNew>chunk(100, txManager)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

    @Bean
    public Step anexoStep(
            JobRepository jobRepository,
            PlatformTransactionManager txManager,
            @Qualifier("anexoReader") ItemReader<AnexoOld> reader,
            AnexoProcessor processor,
            @Qualifier("anexoWriter") ItemWriter<AnexoNew> writer
    ) {
        return new StepBuilder("anexoStep", jobRepository)
                .<AnexoOld, AnexoNew>chunk(50, txManager) // Menor chunk size devido ao conteúdo binário
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }

}
