package com.seOuvMigrate.seOuvMigrate.batch;

import com.seOuvMigrate.seOuvMigrate.domain.SolicitacaoOld;
import com.seOuvMigrate.seOuvMigrate.domain.SolicitacaoNew;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.batch.item.database.JdbcBatchItemWriter;
import org.springframework.batch.item.database.builder.JdbcCursorItemReaderBuilder;
import org.springframework.batch.item.database.builder.JdbcBatchItemWriterBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import javax.sql.DataSource;

@Configuration
public class SolicitacaoMigrationConfig {

    @Bean
    public JdbcCursorItemReader<SolicitacaoOld> solicitacaoReader(
            @Qualifier("oldDataSource") DataSource oldDs) {
        return new JdbcCursorItemReaderBuilder<SolicitacaoOld>()
                .name("solicitacaoReader")
                .dataSource(oldDs)
                .sql("""
                    SELECT
                      idSolicitacao,
                      idCidadao,
                      idEntidades,
                      idAcao,
                      dataIni,
                      status,
                      datafim,
                      titulo,
                      protocolo,
                      tipo,
                      dataLimite,
                      instancia,
                      encaminhada,
                      formaRecebimento,
                      visualizada,
                      avaliacao,
                      sigilo,
                      liberaDenuncia,
                      Oid AS oid,
                      canalEntrada,
                      idResponsavel,
                      protocoloAgrese
                    FROM solicitacao
                    """)
                .rowMapper(new BeanPropertyRowMapper<>(SolicitacaoOld.class))
                .build();
    }

    @Bean
    public JdbcBatchItemWriter<SolicitacaoNew> solicitacaoWriter(
            @Qualifier("dataSource") DataSource newDs) {
        return new JdbcBatchItemWriterBuilder<SolicitacaoNew>()
                .dataSource(newDs)
                .beanMapped()
                .sql("""
                    INSERT INTO se_ouv.solicitacao (
                      id_solicitacao,
                      id_cidadao,
                      id_competencia,
                      id_entidade,
                      data_ini,
                      status,
                      data_fim,
                      titulo,
                      protocolo,
                      tipo,
                      data_limite,
                      instancia,
                      encaminhada,
                      forma_recebimento,
                      visualizada,
                      sigilo,
                      libera_denuncia,
                      oid,
                      data_prazo,
                      data_encerramento,
                      reaberta,
                      canal_entrada,
                      id_responsavel_atendente,
                      protocolo_agrese
                    ) VALUES (
                      :idSolicitacao,
                      :idCidadao,
                      :idCompetencia,
                      :idEntidade,
                      :dataIni,
                      :status,
                      :dataFim,
                      :titulo,
                      :protocolo,
                      :tipo,
                      :dataLimite,
                      :instancia,
                      :encaminhada,
                      :formaRecebimento,
                      :visualizada,
                      :sigilo,
                      :liberaDenuncia,
                      :oid,
                      :dataPrazo,
                      :dataEncerramento,
                      :reaberta,
                      :canalEntrada,
                      :idResponsavelAtendente,
                      :protocoloAgrese
                    )
                    ON CONFLICT (id_solicitacao) DO UPDATE SET
                      id_cidadao                = EXCLUDED.id_cidadao,
                      id_competencia            = EXCLUDED.id_competencia,
                      id_entidade               = EXCLUDED.id_entidade,
                      data_ini                  = EXCLUDED.data_ini,
                      status                    = EXCLUDED.status,
                      data_fim                  = EXCLUDED.data_fim,
                      titulo                    = EXCLUDED.titulo,
                      protocolo                 = EXCLUDED.protocolo,
                      tipo                      = EXCLUDED.tipo,
                      data_limite               = EXCLUDED.data_limite,
                      instancia                 = EXCLUDED.instancia,
                      encaminhada               = EXCLUDED.encaminhada,
                      forma_recebimento         = EXCLUDED.forma_recebimento,
                      visualizada               = EXCLUDED.visualizada,
                      sigilo                    = EXCLUDED.sigilo,
                      libera_denuncia           = EXCLUDED.libera_denuncia,
                      oid                       = EXCLUDED.oid,
                      data_prazo                = EXCLUDED.data_prazo,
                      data_encerramento         = EXCLUDED.data_encerramento,
                      reaberta                  = EXCLUDED.reaberta,
                      canal_entrada             = EXCLUDED.canal_entrada,
                      id_responsavel_atendente  = EXCLUDED.id_responsavel_atendente,
                      protocolo_agrese          = EXCLUDED.protocolo_agrese
                    """)
                .build();
    }
}
