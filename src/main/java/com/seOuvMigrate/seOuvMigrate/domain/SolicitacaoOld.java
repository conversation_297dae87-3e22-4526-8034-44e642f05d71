package com.seOuvMigrate.seOuvMigrate.domain;

import java.sql.Date;
import java.sql.Timestamp;

public class SolicitacaoOld {
    private Integer idSolicitacao;
    private Integer idCidadao;
    private Integer idEntidades;
    private Integer idAcao;
    private Timestamp dataIni;
    private String status;
    private Date datafim;
    private String titulo;
    private String protocolo;
    private String tipo;
    private Date dataLimite;
    private Short instancia;
    private Short encaminhada;
    private Integer formaRecebimento;
    private Short visualizada;
    private Float avaliacao;
    private Short sigilo;
    private Short liberaDenuncia;
    private String oid;
    private Short canalEntrada;
    private Integer idResponsavel;
    private String protocoloAgrese;

    public Integer getIdSolicitacao() {
        return idSolicitacao;
    }

    public void setIdSolicitacao(Integer idSolicitacao) {
        this.idSolicitacao = idSolicitacao;
    }

    public Integer getIdCidadao() {
        return idCidadao;
    }

    public void setIdCidadao(Integer idCidadao) {
        this.idCidadao = idCidadao;
    }

    public Integer getIdEntidades() {
        return idEntidades;
    }

    public void setIdEntidades(Integer idEntidades) {
        this.idEntidades = idEntidades;
    }

    public Integer getIdAcao() {
        return idAcao;
    }

    public void setIdAcao(Integer idAcao) {
        this.idAcao = idAcao;
    }

    public Timestamp getDataIni() {
        return dataIni;
    }

    public void setDataIni(Timestamp dataIni) {
        this.dataIni = dataIni;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getDatafim() {
        return datafim;
    }

    public void setDatafim(Date datafim) {
        this.datafim = datafim;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(String protocolo) {
        this.protocolo = protocolo;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Date getDataLimite() {
        return dataLimite;
    }

    public void setDataLimite(Date dataLimite) {
        this.dataLimite = dataLimite;
    }

    public Short getInstancia() {
        return instancia;
    }

    public void setInstancia(Short instancia) {
        this.instancia = instancia;
    }

    public Short getEncaminhada() {
        return encaminhada;
    }

    public void setEncaminhada(Short encaminhada) {
        this.encaminhada = encaminhada;
    }

    public Integer getFormaRecebimento() {
        return formaRecebimento;
    }

    public void setFormaRecebimento(Integer formaRecebimento) {
        this.formaRecebimento = formaRecebimento;
    }

    public Short getVisualizada() {
        return visualizada;
    }

    public void setVisualizada(Short visualizada) {
        this.visualizada = visualizada;
    }

    public Float getAvaliacao() {
        return avaliacao;
    }

    public void setAvaliacao(Float avaliacao) {
        this.avaliacao = avaliacao;
    }

    public Short getSigilo() {
        return sigilo;
    }

    public void setSigilo(Short sigilo) {
        this.sigilo = sigilo;
    }

    public Short getLiberaDenuncia() {
        return liberaDenuncia;
    }

    public void setLiberaDenuncia(Short liberaDenuncia) {
        this.liberaDenuncia = liberaDenuncia;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public Short getCanalEntrada() {
        return canalEntrada;
    }

    public void setCanalEntrada(Short canalEntrada) {
        this.canalEntrada = canalEntrada;
    }

    public Integer getIdResponsavel() {
        return idResponsavel;
    }

    public void setIdResponsavel(Integer idResponsavel) {
        this.idResponsavel = idResponsavel;
    }

    public String getProtocoloAgrese() {
        return protocoloAgrese;
    }

    public void setProtocoloAgrese(String protocoloAgrese) {
        this.protocoloAgrese = protocoloAgrese;
    }


}
