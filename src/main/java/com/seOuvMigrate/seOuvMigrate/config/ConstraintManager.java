package com.seOuvMigrate.seOuvMigrate.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

@Component
public class ConstraintManager {

    private static final Logger logger = LoggerFactory.getLogger(ConstraintManager.class);
    
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public ConstraintManager(@Qualifier("dataSource") DataSource dataSource) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
    }

    public void disableAllForeignKeyConstraints() {
        logger.info("Iniciando desabilitação de todas as constraints FK...");
        
        try {
            // Listar todas as constraints FK existentes
            List<Map<String, Object>> constraints = listAllForeignKeyConstraints();
            logger.info("Encontradas {} constraints FK para desabilitar", constraints.size());
            
            // Desabilitar cada constraint
            for (Map<String, Object> constraint : constraints) {
                String tableName = (String) constraint.get("table_name");
                String constraintName = (String) constraint.get("constraint_name");
                
                try {
                    String sql = String.format("ALTER TABLE se_ouv.%s DROP CONSTRAINT IF EXISTS %s", 
                                             tableName, constraintName);
                    jdbcTemplate.execute(sql);
                    logger.info("Constraint removida: {} da tabela {}", constraintName, tableName);
                } catch (Exception e) {
                    logger.warn("Erro ao remover constraint {} da tabela {}: {}", 
                              constraintName, tableName, e.getMessage());
                }
            }
            
            // Verificar se ainda existem constraints
            int remainingConstraints = countForeignKeyConstraints();
            logger.info("Constraints FK restantes após desabilitação: {}", remainingConstraints);
            
            // Criar tabela de log se não existir
            createMigrationLogTable();
            
            // Registrar a desabilitação
            jdbcTemplate.update(
                "INSERT INTO se_ouv.migration_log (step_name, notes) VALUES (?, ?)",
                "DISABLE_FK_CONSTRAINTS", 
                "All foreign key constraints disabled before migration via Java"
            );
            
            logger.info("FK constraints desabilitadas com sucesso!");
            
        } catch (Exception e) {
            logger.error("Erro ao desabilitar FK constraints: {}", e.getMessage(), e);
            throw new RuntimeException("Falha ao desabilitar FK constraints", e);
        }
    }

    public void enableAllForeignKeyConstraints() {
        logger.info("Iniciando reabilitação de todas as constraints FK...");
        
        try {
            // Verificar integridade dos dados antes de recriar constraints
            verifyDataIntegrity();
            
            // Recriar constraints FK
            recreateForeignKeyConstraints();
            
            // Verificar quantas constraints foram criadas
            int createdConstraints = countForeignKeyConstraints();
            logger.info("Constraints FK criadas: {}", createdConstraints);
            
            // Registrar a reabilitação
            jdbcTemplate.update(
                "INSERT INTO se_ouv.migration_log (step_name, notes) VALUES (?, ?)",
                "ENABLE_FK_CONSTRAINTS", 
                "All foreign key constraints recreated after migration via Java"
            );
            
            logger.info("FK constraints reabilitadas com sucesso!");
            
        } catch (Exception e) {
            logger.error("Erro ao reabilitar FK constraints: {}", e.getMessage(), e);
            throw new RuntimeException("Falha ao reabilitar FK constraints", e);
        }
    }

    private List<Map<String, Object>> listAllForeignKeyConstraints() {
        String sql = """
            SELECT 
                tc.constraint_name,
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = 'se_ouv'
            ORDER BY tc.table_name, tc.constraint_name
            """;
        
        return jdbcTemplate.queryForList(sql);
    }

    private int countForeignKeyConstraints() {
        String sql = """
            SELECT COUNT(*)
            FROM information_schema.table_constraints AS tc
            WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = 'se_ouv'
            """;
        
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }

    private void createMigrationLogTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS se_ouv.migration_log (
                id SERIAL PRIMARY KEY,
                step_name VARCHAR(100) NOT NULL,
                execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(20) DEFAULT 'SUCCESS',
                notes TEXT
            )
            """;
        
        jdbcTemplate.execute(sql);
    }

    private void verifyDataIntegrity() {
        logger.info("Verificando integridade dos dados antes de recriar constraints...");
        
        // Verificar cidadao -> usuario
        int invalidCidadaoUsuario = jdbcTemplate.queryForObject("""
            SELECT COUNT(*)
            FROM se_ouv.cidadao c
            LEFT JOIN se_ouv.usuario u ON c.id_usuario = u.id_usuario
            WHERE c.id_usuario IS NOT NULL AND u.id_usuario IS NULL
            """, Integer.class);
        
        if (invalidCidadaoUsuario > 0) {
            logger.warn("Encontrados {} cidadãos com id_usuario inválido", invalidCidadaoUsuario);
        }
        
        // Verificar mensagem -> solicitacao
        int invalidMensagemSolicitacao = jdbcTemplate.queryForObject("""
            SELECT COUNT(*)
            FROM se_ouv.mensagem m
            LEFT JOIN se_ouv.solicitacao s ON m.id_solicitacao = s.id_solicitacao
            WHERE m.id_solicitacao IS NOT NULL AND s.id_solicitacao IS NULL
            """, Integer.class);
        
        if (invalidMensagemSolicitacao > 0) {
            logger.warn("Encontradas {} mensagens com id_solicitacao inválido", invalidMensagemSolicitacao);
        }
        
        // Verificar anexo -> mensagem
        int invalidAnexoMensagem = jdbcTemplate.queryForObject("""
            SELECT COUNT(*)
            FROM se_ouv.anexo a
            LEFT JOIN se_ouv.mensagem m ON a.id_mensagem = m.id_mensagem
            WHERE a.id_mensagem IS NOT NULL AND m.id_mensagem IS NULL
            """, Integer.class);
        
        if (invalidAnexoMensagem > 0) {
            logger.warn("Encontrados {} anexos com id_mensagem inválido", invalidAnexoMensagem);
        }
        
        logger.info("Verificação de integridade concluída");
    }

    private void recreateForeignKeyConstraints() {
        logger.info("Recriando constraints FK...");
        
        // Lista de constraints para recriar
        String[] constraints = {
            // Constraints da tabela cidadao
            "ALTER TABLE se_ouv.cidadao ADD CONSTRAINT fk_cidadao_usuario FOREIGN KEY (id_usuario) REFERENCES se_ouv.usuario(id_usuario)",
            "ALTER TABLE se_ouv.cidadao ADD CONSTRAINT fk_cidadao_entidade FOREIGN KEY (id_entidade) REFERENCES se_ouv.entidade(id_entidade)",
            
            // Constraints da tabela responsavel
            "ALTER TABLE se_ouv.responsavel ADD CONSTRAINT fk_responsavel_entidade FOREIGN KEY (id_entidade) REFERENCES se_ouv.entidade(id_entidade)",
            
            // Constraints da tabela competencia
            "ALTER TABLE se_ouv.competencia ADD CONSTRAINT fk_competencia_tema FOREIGN KEY (id_tema) REFERENCES se_ouv.tema(id_tema)",
            "ALTER TABLE se_ouv.competencia ADD CONSTRAINT fk_competencia_entidade FOREIGN KEY (id_entidade) REFERENCES se_ouv.entidade(id_entidade)",
            "ALTER TABLE se_ouv.competencia ADD CONSTRAINT fk_competencia_responsavel FOREIGN KEY (id_responsavel) REFERENCES se_ouv.responsavel(id_responsavel)",
            
            // Constraints da tabela solicitacao
            "ALTER TABLE se_ouv.solicitacao ADD CONSTRAINT fk_solicitacao_cidadao FOREIGN KEY (id_cidadao) REFERENCES se_ouv.cidadao(id_cidadao)",
            "ALTER TABLE se_ouv.solicitacao ADD CONSTRAINT fk_solicitacao_entidade FOREIGN KEY (id_entidade) REFERENCES se_ouv.entidade(id_entidade)",
            "ALTER TABLE se_ouv.solicitacao ADD CONSTRAINT fk_solicitacao_competencia FOREIGN KEY (id_competencia) REFERENCES se_ouv.competencia(id_competencia)",
            "ALTER TABLE se_ouv.solicitacao ADD CONSTRAINT fk_solicitacao_responsavel FOREIGN KEY (id_responsavel_atendente) REFERENCES se_ouv.responsavel(id_responsavel)",
            
            // Constraints da tabela mensagem
            "ALTER TABLE se_ouv.mensagem ADD CONSTRAINT fk_mensagem_solicitacao FOREIGN KEY (id_solicitacao) REFERENCES se_ouv.solicitacao(id_solicitacao)",
            "ALTER TABLE se_ouv.mensagem ADD CONSTRAINT fk_mensagem_usuario FOREIGN KEY (id_usuario) REFERENCES se_ouv.usuario(id_usuario)",
            "ALTER TABLE se_ouv.mensagem ADD CONSTRAINT fk_mensagem_responsavel FOREIGN KEY (id_responsavel) REFERENCES se_ouv.responsavel(id_responsavel)",
            
            // Constraints da tabela anexo
            "ALTER TABLE se_ouv.anexo ADD CONSTRAINT fk_anexo_mensagem FOREIGN KEY (id_mensagem) REFERENCES se_ouv.mensagem(id_mensagem)"
        };
        
        for (String constraint : constraints) {
            try {
                jdbcTemplate.execute(constraint);
                logger.info("Constraint criada: {}", constraint.substring(constraint.indexOf("fk_")));
            } catch (Exception e) {
                logger.warn("Erro ao criar constraint: {} - {}", constraint, e.getMessage());
            }
        }
    }
}
