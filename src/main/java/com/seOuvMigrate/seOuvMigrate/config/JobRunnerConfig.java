package com.seOuvMigrate.seOuvMigrate.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobExecution;

@Configuration
public class JobRunnerConfig {

    private static final Logger logger = LoggerFactory.getLogger(JobRunnerConfig.class);

    @Bean
    public ApplicationRunner starter(<PERSON><PERSON>auncher jobLauncher,
                                     Job migrateUsuarioJob,
                                     ConstraintManager constraintManager) {
        return args -> {
            logger.info("=== INICIANDO PROCESSO DE MIGRAÇÃO ===");

            try {
                // ETAPA 1: Desabilitar constraints FK
                logger.info("ETAPA 1: Desabilitando constraints FK...");
                constraintManager.disableAllForeignKeyConstraints();

                // ETAPA 2: Executar migração
                logger.info("ETAPA 2: Executando migração Spring Batch...");
                var params = new JobParametersBuilder()
                        .addLong("runAt", System.currentTimeMillis())
                        .toJobParameters();

                JobExecution jobExecution = jobLauncher.run(migrateUsuarioJob, params);

                // ETAPA 3: Verificar resultado e reabilitar constraints
                if (jobExecution.getStatus().isUnsuccessful()) {
                    logger.error("MIGRAÇÃO FALHOU! Status: {}", jobExecution.getStatus());
                    logger.error("Exit Code: {}", jobExecution.getExitStatus().getExitCode());
                    logger.error("Exit Message: {}", jobExecution.getExitStatus().getExitDescription());
                } else {
                    logger.info("MIGRAÇÃO CONCLUÍDA COM SUCESSO! Status: {}", jobExecution.getStatus());
                }

                // ETAPA 4: Reabilitar constraints FK (sempre executar, mesmo se a migração falhou)
                logger.info("ETAPA 3: Reabilitando constraints FK...");
                constraintManager.enableAllForeignKeyConstraints();

                logger.info("=== PROCESSO DE MIGRAÇÃO FINALIZADO ===");

            } catch (Exception e) {
                logger.error("ERRO CRÍTICO durante o processo de migração: {}", e.getMessage(), e);

                // Tentar reabilitar constraints mesmo em caso de erro
                try {
                    logger.info("Tentando reabilitar constraints FK após erro...");
                    constraintManager.enableAllForeignKeyConstraints();
                } catch (Exception constraintError) {
                    logger.error("ERRO ao reabilitar constraints FK: {}", constraintError.getMessage(), constraintError);
                }

                throw e;
            }
        };
    }
}
